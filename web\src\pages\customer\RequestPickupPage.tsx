import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ArrowLeft, MapPin, Phone, Package, Clock, User, FileText, Truck, Shield, Star, Zap, CheckCircle, Search, Map,
  Smartphone, ShoppingBag, Coffee, Gift, Book, ChevronDown
} from 'lucide-react';

interface Location {
  lat: number;
  lng: number;
  address: string;
}

interface PackageTypeOption {
  id: string;
  name: string;
  icon: React.ReactNode;
  description: string;
  estimatedTime: string;
  basePrice: number;
}

const RequestPickupPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Scroll and animation states
  const [scrollY, setScrollY] = useState(0);
  const [isHeaderCompact, setIsHeaderCompact] = useState(false);

  // Form states
  const [pickupLocation, setPickupLocation] = useState<Location | null>(null);
  const [deliveryLocation, setDeliveryLocation] = useState<Location | null>(null);
  const [senderName, setSenderName] = useState('');
  const [senderPhone, setSenderPhone] = useState('');
  const [receiverName, setReceiverName] = useState('');
  const [receiverPhone, setReceiverPhone] = useState('');
  const [packageType, setPackageType] = useState('');
  const [packageSize, setPackageSize] = useState('small');
  const [packageWeight, setPackageWeight] = useState('');
  const [packageValue, setPackageValue] = useState('');
  const [specialInstructions, setSpecialInstructions] = useState('');
  const [preferredTime, setPreferredTime] = useState('asap');
  const [customTime, setCustomTime] = useState('');
  const [showPackageTypes, setShowPackageTypes] = useState(false);

  // Handle scroll for header animation with debouncing
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      setScrollY(currentScrollY);

      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        setIsHeaderCompact(currentScrollY > 200);
      }, 50);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(timeoutId);
    };
  }, []);

  // Location selection handlers
  const handleLocationSelect = (type: 'pickup' | 'delivery') => {
    console.log(`Navigating to location selection for ${type}`);
    navigate(`/customer/select-location?type=${type}`);
  };

  // Package type selection handler
  const handlePackageTypeSelect = (typeId: string) => {
    setPackageType(typeId);
    setShowPackageTypes(false);
  };

  // Listen for location updates from SelectLocationPage
  useEffect(() => {
    const handleLocationUpdate = (event: CustomEvent) => {
      console.log('Location update event received:', event.detail);
      const locationData = event.detail;

      if (locationData && locationData.lat && locationData.lng && locationData.address) {
        const newLocation: Location = {
          lat: locationData.lat,
          lng: locationData.lng,
          address: locationData.address
        };

        if (locationData.type === 'pickup') {
          setPickupLocation(newLocation);
          localStorage.removeItem('selectedPickupLocation');
        } else if (locationData.type === 'delivery') {
          setDeliveryLocation(newLocation);
          localStorage.removeItem('selectedDeliveryLocation');
        }
      }
    };

    window.addEventListener('locationSelected', handleLocationUpdate as EventListener);
    return () => {
      window.removeEventListener('locationSelected', handleLocationUpdate as EventListener);
    };
  }, []);

  const packageSizes = [
    {
      value: 'small',
      label: 'Small Package',
      description: 'Up to 30cm',
      price: 15,
      icon: Package,
      color: 'from-blue-500 to-blue-600',
      estimatedTime: '1-2 hours'
    },
    {
      value: 'medium',
      label: 'Medium Package',
      description: '30-60cm',
      price: 25,
      icon: Package,
      color: 'from-green-500 to-green-600',
      estimatedTime: '2-3 hours'
    },
    {
      value: 'large',
      label: 'Large Package',
      description: '60-100cm',
      price: 35,
      icon: Package,
      color: 'from-orange-500 to-orange-600',
      estimatedTime: '3-4 hours'
    },
    {
      value: 'extra-large',
      label: 'Extra Large',
      description: '100cm+',
      price: 50,
      icon: Package,
      color: 'from-red-500 to-red-600',
      estimatedTime: '4-6 hours'
    }
  ];

  // Package type options with enhanced details
  const packageTypes: PackageTypeOption[] = [
    {
      id: 'documents',
      name: 'Documents',
      icon: <FileText size={24} />,
      description: 'Papers, contracts, certificates',
      estimatedTime: '2-4 hours',
      basePrice: 15
    },
    {
      id: 'electronics',
      name: 'Electronics',
      icon: <Smartphone size={24} />,
      description: 'Phones, laptops, gadgets',
      estimatedTime: '3-6 hours',
      basePrice: 25
    },
    {
      id: 'clothing',
      name: 'Clothing',
      icon: <ShoppingBag size={24} />,
      description: 'Clothes, shoes, accessories',
      estimatedTime: '4-8 hours',
      basePrice: 20
    },
    {
      id: 'food',
      name: 'Food & Drinks',
      icon: <Coffee size={24} />,
      description: 'Meals, beverages, snacks',
      estimatedTime: '1-2 hours',
      basePrice: 12
    },
    {
      id: 'gifts',
      name: 'Gifts',
      icon: <Gift size={24} />,
      description: 'Presents, flowers, surprises',
      estimatedTime: '2-4 hours',
      basePrice: 18
    },
    {
      id: 'books',
      name: 'Books & Media',
      icon: <Book size={24} />,
      description: 'Books, CDs, magazines',
      estimatedTime: '4-6 hours',
      basePrice: 16
    }
  ];

  const isFormValid = () => {
    return pickupLocation &&
           deliveryLocation &&
           senderName.trim() &&
           senderPhone.trim() &&
           receiverName.trim() &&
           receiverPhone.trim() &&
           packageType.trim() &&
           packageWeight.trim();
  };

  const getSelectedSizePrice = () => {
    const size = packageSizes.find(s => s.value === packageSize);
    return size ? size.price : 0;
  };

  const handleSubmit = () => {
    if (!isFormValid()) {
      alert('Please fill in all required fields');
      return;
    }

    const pickupRequest = {
      id: `PU-${Date.now().toString().slice(-6)}`,
      pickupAddress: pickupLocation?.address || '',
      deliveryAddress: deliveryLocation?.address || '',
      pickupLocation,
      deliveryLocation,
      senderName,
      senderPhone,
      receiverName,
      receiverPhone,
      packageType,
      packageSize,
      packageWeight,
      packageValue,
      specialInstructions,
      preferredTime: preferredTime === 'custom' ? customTime : preferredTime,
      price: getSelectedSizePrice(),
      status: 'Pending',
      createdAt: new Date().toISOString()
    };

    // Navigate to confirmation page
    navigate('/customer/request-pickup-confirmation', { 
      state: { pickupRequest } 
    });
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Premium Animated Background */}
      <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        {/* Animated gradient orbs */}
        <motion.div
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-orange-500/30 to-red-600/30 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.4, 0.7, 0.4],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
          className="absolute top-1/3 right-0 w-80 h-80 bg-gradient-to-br from-purple-500/30 to-blue-600/30 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.2, 0.5, 0.2],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 4
          }}
          className="absolute bottom-0 left-1/3 w-72 h-72 bg-gradient-to-br from-green-500/20 to-teal-600/20 rounded-full blur-3xl"
        />

        {/* Floating particles */}
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            animate={{
              y: [0, -100, 0],
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 5,
            }}
            className="absolute w-1 h-1 bg-white/20 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
          />
        ))}
      </div>

      {/* Sticky Header with Scroll Animation */}
      <motion.div
        className="fixed left-0 right-0 transition-all duration-500"
        animate={{
          top: "64px",
          zIndex: isHeaderCompact ? 45 : 35,
          backgroundColor: isHeaderCompact
            ? "rgba(15, 23, 42, 0.95)"
            : "rgba(15, 23, 42, 0)",
          backdropFilter: isHeaderCompact ? "blur(20px)" : "blur(0px)",
          borderBottom: isHeaderCompact
            ? "1px solid rgba(255, 255, 255, 0.1)"
            : "1px solid rgba(255, 255, 255, 0)",
        }}
        transition={{ duration: 0.3 }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            animate={{
              paddingTop: isHeaderCompact ? "1rem" : "2rem",
              paddingBottom: isHeaderCompact ? "1rem" : "2rem",
            }}
            transition={{ duration: 0.3 }}
            className="flex items-center justify-between"
          >
            <div className="flex items-center gap-4">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => navigate(-1)}
                className="p-3 bg-white/10 backdrop-blur-xl rounded-xl border border-white/20 hover:bg-white/20 transition-colors"
              >
                <ArrowLeft className="w-5 h-5 text-white" />
              </motion.button>

              <motion.div
                animate={{
                  fontSize: isHeaderCompact ? "1.5rem" : "2rem",
                }}
                transition={{ duration: 0.3 }}
                className="font-bold text-white"
              >
                Request Pickup
              </motion.div>
            </div>

            {/* Compact Search when header is compact */}
            <AnimatePresence>
              {isHeaderCompact && (
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  className="flex-1 max-w-md mx-8"
                >
                  <div className="bg-white/10 backdrop-blur-xl rounded-xl border border-white/20">
                    <div className="flex items-center gap-3 p-3">
                      <Search size={18} className="text-white/60" />
                      <input
                        type="text"
                        placeholder="Search..."
                        className="flex-1 bg-transparent text-white placeholder-white/50 outline-none text-sm"
                      />
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </div>
      </motion.div>

      {/* Hero Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        className="relative z-10 pt-16 pb-16"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="mb-8"
          >
            <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-white via-orange-100 to-red-100 bg-clip-text text-transparent mb-4">
              Request Pickup
            </h1>
            <p className="text-white/70 text-xl font-medium max-w-2xl mx-auto">
              Fast, reliable, and secure package pickup service at your doorstep
            </p>
          </motion.div>

          {/* Quick Stats */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto mb-12"
          >
            {[
              { icon: Zap, label: 'Fast Pickup', value: '< 2 Hours' },
              { icon: Shield, label: 'Insured', value: 'Up to ₪500' },
              { icon: Star, label: 'Rating', value: '4.9/5' }
            ].map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6"
              >
                <stat.icon className="w-8 h-8 text-orange-400 mx-auto mb-2" />
                <div className="text-2xl font-bold text-white">{stat.value}</div>
                <div className="text-white/60 text-sm">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="relative z-10 min-h-screen px-4 sm:px-6 lg:px-8 pb-20">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
            {/* Main Form */}
            <div className="xl:col-span-2 space-y-6">
              {/* Pickup & Delivery Addresses */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-8 shadow-2xl"
              >
                <h2 className="text-xl font-bold text-white mb-6 flex items-center gap-3">
                  <motion.div
                    animate={{ rotate: [0, 10, -10, 0] }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="p-2 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl"
                  >
                    <MapPin className="w-6 h-6 text-white" />
                  </motion.div>
                  Pickup & Delivery Locations
                </h2>

                <div className="space-y-6">
                  {/* Pickup Location */}
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.2 }}
                  >
                    <label className="block text-sm font-semibold text-white/90 mb-3">
                      Pickup Location <span className="text-red-400">*</span>
                    </label>
                    <div className="flex gap-3">
                      <div className="flex-1 relative">
                        <input
                          type="text"
                          value={pickupLocation?.address || ''}
                          placeholder="Click 'Select on Map' to choose pickup location"
                          readOnly
                          className="w-full px-4 py-4 bg-white/10 border border-white/30 rounded-xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-orange-400 focus:border-transparent backdrop-blur-sm cursor-pointer"
                          onClick={() => handleLocationSelect('pickup')}
                        />
                        {pickupLocation && (
                          <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            className="absolute top-3 right-3"
                          >
                            <CheckCircle className="text-orange-400" size={20} />
                          </motion.div>
                        )}
                      </div>
                      <motion.button
                        onClick={() => handleLocationSelect('pickup')}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="px-6 py-4 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-semibold rounded-xl transition-all duration-200 flex items-center gap-2"
                      >
                        <Map size={18} />
                        Select on Map
                      </motion.button>
                    </div>
                    {pickupLocation && (
                      <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="mt-2 text-orange-400 text-sm flex items-center gap-2"
                      >
                        <MapPin size={14} />
                        <span>Pickup location confirmed</span>
                      </motion.div>
                    )}
                  </motion.div>

                  {/* Delivery Location */}
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.3 }}
                  >
                    <label className="block text-sm font-semibold text-white/90 mb-3">
                      Delivery Location <span className="text-red-400">*</span>
                    </label>
                    <div className="flex gap-3">
                      <div className="flex-1 relative">
                        <input
                          type="text"
                          value={deliveryLocation?.address || ''}
                          placeholder="Click 'Select on Map' to choose delivery location"
                          readOnly
                          className="w-full px-4 py-4 bg-white/10 border border-white/30 rounded-xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent backdrop-blur-sm cursor-pointer"
                          onClick={() => handleLocationSelect('delivery')}
                        />
                        {deliveryLocation && (
                          <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            className="absolute top-3 right-3"
                          >
                            <CheckCircle className="text-purple-400" size={20} />
                          </motion.div>
                        )}
                      </div>
                      <motion.button
                        onClick={() => handleLocationSelect('delivery')}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="px-6 py-4 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-semibold rounded-xl transition-all duration-200 flex items-center gap-2"
                      >
                        <Map size={18} />
                        Select on Map
                      </motion.button>
                    </div>
                    {deliveryLocation && (
                      <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="mt-2 text-purple-400 text-sm flex items-center gap-2"
                      >
                        <MapPin size={14} />
                        <span>Delivery location confirmed</span>
                      </motion.div>
                    )}
                  </motion.div>
                </div>
              </motion.div>

              {/* Contact Information */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-8 shadow-2xl"
              >
                <h2 className="text-xl font-bold text-white mb-6 flex items-center gap-3">
                  <motion.div
                    animate={{ scale: [1, 1.1, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="p-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl"
                  >
                    <User className="w-6 h-6 text-white" />
                  </motion.div>
                  Contact Information
                </h2>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.3 }}
                    className="space-y-4"
                  >
                    <h3 className="font-semibold text-white/90 text-lg">Sender Details</h3>
                    <div>
                      <label className="block text-sm font-semibold text-white/90 mb-3">
                        Sender Name *
                      </label>
                      <input
                        type="text"
                        value={senderName}
                        onChange={(e) => setSenderName(e.target.value)}
                        placeholder="Full name"
                        className="w-full p-4 bg-white/10 backdrop-blur-xl border border-white/20 rounded-xl text-white placeholder-white/50 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-semibold text-white/90 mb-3">
                        Sender Phone *
                      </label>
                      <input
                        type="tel"
                        value={senderPhone}
                        onChange={(e) => setSenderPhone(e.target.value)}
                        placeholder="+970 XXX XXX XXX"
                        className="w-full p-4 bg-white/10 backdrop-blur-xl border border-white/20 rounded-xl text-white placeholder-white/50 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                        required
                      />
                    </div>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.4 }}
                    className="space-y-4"
                  >
                    <h3 className="font-semibold text-white/90 text-lg">Receiver Details</h3>
                    <div>
                      <label className="block text-sm font-semibold text-white/90 mb-3">
                        Receiver Name *
                      </label>
                      <input
                        type="text"
                        value={receiverName}
                        onChange={(e) => setReceiverName(e.target.value)}
                        placeholder="Full name"
                        className="w-full p-4 bg-white/10 backdrop-blur-xl border border-white/20 rounded-xl text-white placeholder-white/50 focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-semibold text-white/90 mb-3">
                        Receiver Phone *
                      </label>
                      <input
                        type="tel"
                        value={receiverPhone}
                        onChange={(e) => setReceiverPhone(e.target.value)}
                        placeholder="+970 XXX XXX XXX"
                        className="w-full p-4 bg-white/10 backdrop-blur-xl border border-white/20 rounded-xl text-white placeholder-white/50 focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                        required
                      />
                    </div>
                  </motion.div>
                </div>
              </motion.div>

              {/* Package Details */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-8 shadow-2xl"
              >
                <h2 className="text-xl font-bold text-white mb-6 flex items-center gap-3">
                  <motion.div
                    animate={{ rotateY: [0, 180, 360] }}
                    transition={{ duration: 3, repeat: Infinity }}
                    className="p-2 bg-gradient-to-r from-green-500 to-teal-500 rounded-xl"
                  >
                    <Package className="w-6 h-6 text-white" />
                  </motion.div>
                  Package Details
                </h2>

                <div className="space-y-6">
                  <div className="space-y-6">
                    {/* Package Type Selection */}
                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: 0.4 }}
                    >
                      <label className="block text-sm font-semibold text-white/90 mb-4">
                        Package Type <span className="text-red-400">*</span>
                      </label>

                      {packageType ? (
                        <motion.div
                          initial={{ opacity: 0, scale: 0.9 }}
                          animate={{ opacity: 1, scale: 1 }}
                          className="mb-4"
                        >
                          {(() => {
                            const selectedType = packageTypes.find(type => type.id === packageType);
                            return selectedType ? (
                              <div className="bg-gradient-to-r from-green-500/20 to-teal-500/20 border border-green-400/30 rounded-xl p-4">
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center gap-3">
                                    <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-teal-500 rounded-xl flex items-center justify-center">
                                      {selectedType.icon}
                                    </div>
                                    <div>
                                      <div className="text-white font-semibold">{selectedType.name}</div>
                                      <div className="text-white/70 text-sm">{selectedType.description}</div>
                                    </div>
                                  </div>
                                  <motion.button
                                    onClick={() => setShowPackageTypes(true)}
                                    whileHover={{ scale: 1.05 }}
                                    className="text-white/80 hover:text-white text-sm font-medium"
                                  >
                                    Change
                                  </motion.button>
                                </div>
                              </div>
                            ) : null;
                          })()}
                        </motion.div>
                      ) : (
                        <motion.button
                          onClick={() => setShowPackageTypes(true)}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          className="w-full p-4 bg-white/10 border border-white/30 rounded-xl text-white/70 hover:text-white hover:border-white/50 transition-all duration-200 flex items-center justify-between"
                        >
                          <span>Select package type</span>
                          <ChevronDown size={20} />
                        </motion.button>
                      )}

                      {/* Package Types Grid */}
                      <AnimatePresence>
                        {showPackageTypes && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                            className="mt-4 grid grid-cols-2 md:grid-cols-3 gap-3"
                          >
                            {packageTypes.map((type, index) => (
                              <motion.button
                                key={type.id}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: index * 0.1 }}
                                onClick={() => handlePackageTypeSelect(type.id)}
                                whileHover={{ scale: 1.05, y: -2 }}
                                whileTap={{ scale: 0.95 }}
                                className="bg-white/10 hover:bg-white/20 border border-white/20 hover:border-white/40 rounded-xl p-4 transition-all duration-200 text-left"
                              >
                                <div className="flex flex-col items-center text-center">
                                  <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-teal-500 rounded-xl flex items-center justify-center mb-3">
                                    {type.icon}
                                  </div>
                                  <div className="text-white font-semibold text-sm">{type.name}</div>
                                  <div className="text-white/60 text-xs mt-1">{type.description}</div>
                                  <div className="text-green-400 text-xs font-medium mt-1">
                                    ₪{type.basePrice} • {type.estimatedTime}
                                  </div>
                                </div>
                              </motion.button>
                            ))}
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </motion.div>

                    {/* Package Weight */}
                    <motion.div
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: 0.5 }}
                    >
                      <label className="block text-sm font-semibold text-white/90 mb-3">
                        Package Weight *
                      </label>
                      <input
                        type="text"
                        value={packageWeight}
                        onChange={(e) => setPackageWeight(e.target.value)}
                        placeholder="e.g., 2kg, 500g"
                        className="w-full p-4 bg-white/10 backdrop-blur-xl border border-white/20 rounded-xl text-white placeholder-white/50 focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300"
                        required
                      />
                    </motion.div>
                  </div>

                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.6 }}
                  >
                    <label className="block text-sm font-semibold text-white/90 mb-4">
                      Package Size *
                    </label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {packageSizes.map((size, index) => (
                        <motion.label
                          key={size.value}
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ duration: 0.4, delay: 0.7 + index * 0.1 }}
                          className={`relative p-6 border-2 rounded-2xl cursor-pointer transition-all duration-300 ${
                            packageSize === size.value
                              ? 'border-green-400 bg-green-500/20 shadow-lg shadow-green-500/25'
                              : 'border-white/20 bg-white/5 hover:border-white/40 hover:bg-white/10'
                          }`}
                        >
                          <input
                            type="radio"
                            name="packageSize"
                            value={size.value}
                            checked={packageSize === size.value}
                            onChange={(e) => setPackageSize(e.target.value)}
                            className="sr-only"
                          />
                          <div className="flex items-center gap-4">
                            <div className={`p-3 rounded-xl bg-gradient-to-r ${size.color}`}>
                              <size.icon className="w-6 h-6 text-white" />
                            </div>
                            <div className="flex-1">
                              <div className="font-semibold text-white text-lg">{size.label}</div>
                              <div className="text-white/60 text-sm">{size.description}</div>
                              <div className="text-white/80 text-sm mt-1">{size.estimatedTime}</div>
                            </div>
                            <div className="text-right">
                              <div className="text-2xl font-bold text-white">₪{size.price}</div>
                              {packageSize === size.value && (
                                <motion.div
                                  initial={{ scale: 0 }}
                                  animate={{ scale: 1 }}
                                  className="absolute top-3 right-3"
                                >
                                  <CheckCircle className="w-6 h-6 text-green-400" />
                                </motion.div>
                              )}
                            </div>
                          </div>
                        </motion.label>
                      ))}
                    </div>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.8 }}
                  >
                    <label className="block text-sm font-semibold text-white/90 mb-3">
                      Package Value (Optional)
                    </label>
                    <input
                      type="text"
                      value={packageValue}
                      onChange={(e) => setPackageValue(e.target.value)}
                      placeholder="Estimated value in ₪"
                      className="w-full p-4 bg-white/10 backdrop-blur-xl border border-white/20 rounded-xl text-white placeholder-white/50 focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300"
                    />
                  </motion.div>
                </div>
              </motion.div>

              {/* Timing & Instructions */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-8 shadow-2xl"
              >
                <h2 className="text-xl font-bold text-white mb-6 flex items-center gap-3">
                  <motion.div
                    animate={{ rotate: [0, 360] }}
                    transition={{ duration: 4, repeat: Infinity, ease: "linear" }}
                    className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl"
                  >
                    <Clock className="w-6 h-6 text-white" />
                  </motion.div>
                  Timing & Special Instructions
                </h2>

                <div className="space-y-6">
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.5 }}
                  >
                    <label className="block text-sm font-semibold text-white/90 mb-4">
                      Preferred Pickup Time
                    </label>
                    <div className="space-y-3">
                      {[
                        { value: 'asap', label: 'As soon as possible', icon: Zap },
                        { value: 'today', label: 'Later today', icon: Clock },
                        { value: 'custom', label: 'Specific time', icon: User }
                      ].map((option, index) => (
                        <motion.label
                          key={option.value}
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.3, delay: 0.6 + index * 0.1 }}
                          className={`flex items-center gap-4 p-4 rounded-xl cursor-pointer transition-all duration-300 ${
                            preferredTime === option.value
                              ? 'bg-purple-500/30 border-2 border-purple-400'
                              : 'bg-white/5 border-2 border-white/10 hover:bg-white/10'
                          }`}
                        >
                          <input
                            type="radio"
                            name="preferredTime"
                            value={option.value}
                            checked={preferredTime === option.value}
                            onChange={(e) => setPreferredTime(e.target.value)}
                            className="sr-only"
                          />
                          <option.icon className="w-5 h-5 text-white/80" />
                          <span className="text-white font-medium">{option.label}</span>
                          {preferredTime === option.value && (
                            <motion.div
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              className="ml-auto"
                            >
                              <CheckCircle className="w-5 h-5 text-purple-400" />
                            </motion.div>
                          )}
                        </motion.label>
                      ))}
                    </div>

                    <AnimatePresence>
                      {preferredTime === 'custom' && (
                        <motion.input
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          type="datetime-local"
                          value={customTime}
                          onChange={(e) => setCustomTime(e.target.value)}
                          className="mt-4 w-full p-4 bg-white/10 backdrop-blur-xl border border-white/20 rounded-xl text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                        />
                      )}
                    </AnimatePresence>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.7 }}
                  >
                    <label className="block text-sm font-semibold text-white/90 mb-3 flex items-center gap-2">
                      <FileText className="w-5 h-5" />
                      Special Instructions (Optional)
                    </label>
                    <textarea
                      value={specialInstructions}
                      onChange={(e) => setSpecialInstructions(e.target.value)}
                      placeholder="Any special handling instructions, fragile items, access codes, etc..."
                      className="w-full p-4 bg-white/10 backdrop-blur-xl border border-white/20 rounded-xl text-white placeholder-white/50 focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                      rows={4}
                    />
                  </motion.div>
                </div>
              </motion.div>
            </div>

            {/* Enhanced Summary Sidebar */}
            <div className="xl:col-span-1">
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-8 shadow-2xl sticky top-8"
              >
                <h3 className="text-xl font-bold text-white mb-6 flex items-center gap-3">
                  <motion.div
                    animate={{ scale: [1, 1.1, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="p-2 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl"
                  >
                    <Truck className="w-6 h-6 text-white" />
                  </motion.div>
                  Pickup Summary
                </h3>

                <div className="space-y-4 mb-8">
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: 0.6 }}
                    className="flex justify-between items-center p-4 bg-white/5 rounded-xl"
                  >
                    <span className="text-white/80">Service Fee</span>
                    <span className="text-2xl font-bold text-white">₪{getSelectedSizePrice()}</span>
                  </motion.div>

                  <div className="h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>

                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: 0.7 }}
                    className="flex justify-between items-center p-4 bg-gradient-to-r from-orange-500/20 to-red-500/20 rounded-xl border border-orange-400/30"
                  >
                    <span className="text-white font-semibold">Total</span>
                    <span className="text-3xl font-bold text-white">₪{getSelectedSizePrice()}</span>
                  </motion.div>
                </div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.8 }}
                  className="space-y-3 text-sm text-white/70 mb-8"
                >
                  {[
                    { icon: Zap, text: 'Free pickup within 2 hours' },
                    { icon: Truck, text: 'Real-time tracking included' },
                    { icon: Shield, text: 'Insurance up to ₪500' },
                    { icon: Star, text: '24/7 customer support' }
                  ].map((feature, index) => (
                    <motion.div
                      key={feature.text}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: 0.9 + index * 0.1 }}
                      className="flex items-center gap-3 p-3 bg-white/5 rounded-lg"
                    >
                      <feature.icon className="w-4 h-4 text-orange-400" />
                      <span>{feature.text}</span>
                    </motion.div>
                  ))}
                </motion.div>

                <motion.button
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 1.0 }}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={handleSubmit}
                  disabled={!isFormValid()}
                  className={`w-full py-4 rounded-xl font-bold text-lg transition-all duration-300 ${
                    isFormValid()
                      ? 'bg-gradient-to-r from-orange-500 to-red-500 text-white hover:from-orange-600 hover:to-red-600 shadow-lg shadow-orange-500/25'
                      : 'bg-white/10 text-white/50 cursor-not-allowed'
                  }`}
                >
                  {isFormValid() ? (
                    <span className="flex items-center justify-center gap-2">
                      <Truck className="w-5 h-5" />
                      Request Pickup - ₪{getSelectedSizePrice()}
                    </span>
                  ) : (
                    'Complete form to continue'
                  )}
                </motion.button>

                {/* Progress Indicator */}
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5, delay: 1.1 }}
                  className="mt-6 p-4 bg-white/5 rounded-xl"
                >
                  <div className="text-white/60 text-xs mb-2">Form Completion</div>
                  <div className="w-full bg-white/10 rounded-full h-2">
                    <motion.div
                      initial={{ width: 0 }}
                      animate={{
                        width: `${(Object.values({
                          pickupLocation: pickupLocation?.address || '',
                          deliveryLocation: deliveryLocation?.address || '',
                          senderName: senderName.trim(),
                          senderPhone: senderPhone.trim(),
                          receiverName: receiverName.trim(),
                          receiverPhone: receiverPhone.trim(),
                          packageType: packageType.trim(),
                          packageWeight: packageWeight.trim()
                        }).filter(Boolean).length / 8) * 100}%`
                      }}
                      transition={{ duration: 0.5 }}
                      className="bg-gradient-to-r from-orange-500 to-red-500 h-2 rounded-full"
                    />
                  </div>
                </motion.div>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RequestPickupPage;
