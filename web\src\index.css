@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
}

* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background-color: #0f172a;
  overflow-x: hidden;
}

#root {
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Custom components */
@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }

  .card {
    @apply bg-white rounded-lg shadow-md p-6;
  }

  .page-container {
    @apply min-h-screen bg-gray-50;
  }

  .content-wrapper {
    @apply w-full;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Enhanced logo animations */
  .logo-glow {
    animation: logoGlow 3s ease-in-out infinite alternate;
  }

  @keyframes logoGlow {
    from {
      text-shadow: 0 0 20px rgba(117, 41, 179, 0.5), 0 0 40px rgba(103, 179, 41, 0.3);
    }
    to {
      text-shadow: 0 0 30px rgba(147, 51, 234, 0.8), 0 0 60px rgba(251, 191, 36, 0.5), 0 0 80px rgba(103, 179, 41, 0.4);
    }
  }

  .lightning-pulse {
    animation: lightningPulse 2s ease-in-out infinite;
  }

  @keyframes lightningPulse {
    0%, 100% {
      filter: brightness(1) saturate(1);
    }
    50% {
      filter: brightness(1.3) saturate(1.5);
    }
  }

  .electric-text {
    background: linear-gradient(45deg, #9333EA, #7529B3, #FBBF24, #67B329, #059669);
    background-size: 300% 300%;
    animation: electricFlow 3s ease-in-out infinite;
  }

  @keyframes electricFlow {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }
}
